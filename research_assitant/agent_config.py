from langroid.agent.special.doc_qa_agent import DocQAA<PERSON>, DocQAAgentConfig
from langroid.vector_store.chromadb import ChromaDBConfig

def create_doc_agent():
    vector_cfg = ChromaDBConfig(
        persist_path="./db",
        collection_name="research-docs",
    )

    config = DocQAAgentConfig(
        system_message="You're a helpful AI researcher. Use only facts from the documents unless told otherwise.",
        vector_db_config=vector_cfg,
        use_mcp=True,  # 🔥 MCP is enabled here
        chunk_size=300,
        overlap=50,
        use_qa_template=True,
        use_vector_db=True,
    )

    return DocQAAgent(config)
