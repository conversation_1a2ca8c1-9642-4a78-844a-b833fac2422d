from agent_config import create_doc_agent

def run():
    agent = create_doc_agent()
    print("Ingesting documents...")
    agent.ingest("docs/sample.txt")

    print("Ready. Ask anything about the documents.")
    while True:
        query = input("\n> ")
        if query.lower() in {"exit", "quit"}:
            print("Goodbye!")
            break
        response = agent.run(query)
        print("\n🧠 Answer:\n", response)

if __name__ == "__main__":
    run()
